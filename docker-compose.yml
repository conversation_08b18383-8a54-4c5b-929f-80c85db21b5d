services:
  api:
    build: .
    restart: always
    env_file:
      - .env
    environment:
      DATABASE_URL: *****************************************************
      LOG_DIR: /app/logs
    ports:
      - "8000:8000"  # Allow both IPv4 and IPv6 connections
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - /root/secretfiles/secret_files.json:/app/src/app/utils/firebase/secret_files.json
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/healthcheck"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

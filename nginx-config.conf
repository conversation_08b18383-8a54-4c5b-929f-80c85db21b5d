server {
    server_name dashboard.inqilabgroup.com;

    # SSL session settings for better performance (timeout is in options-ssl-nginx.conf)
    ssl_session_cache shared:SSL:10m;

    # Client connection settings
    client_max_body_size 100M;
    client_body_timeout 60s;
    client_header_timeout 60s;

    # Keep-alive settings
    keepalive_timeout 65s;
    keepalive_requests 100;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Connection and timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        proxy_buffering off;
        proxy_request_buffering off;

        # Keep-alive settings
        proxy_http_version 1.1;
        proxy_set_header Connection "";

        # Handle client disconnections gracefully
        proxy_ignore_client_abort off;

        # Buffer settings for better performance
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }

    location /uploads/ {
        alias /root/IPM/uploads/;
        autoindex off;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        add_header Content-Disposition "inline";
        try_files $uri $uri/ =404;
    }

    location /static/ {
        alias /root/IPM/src/app/static/;
        autoindex off;
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
        try_files $uri $uri/ =404;
    }

    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/dashboard.inqilabgroup.com/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/dashboard.inqilabgroup.com/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}

server {
    if ($host = dashboard.inqilabgroup.com) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    server_name dashboard.inqilabgroup.com;
    listen 80;
    return 404; # managed by Certbot
}

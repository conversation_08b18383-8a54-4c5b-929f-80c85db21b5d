/* Main CSS file for the IPM Admin Panel */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header styles */
header {
    background-color: #2c3e50;
    color: white;
    padding: 1rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-name {
    margin-right: 15px;
}

.logout-btn {
    background-color: #e74c3c;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
}

/* Navigation */
nav {
    background-color: #34495e;
    padding: 0.5rem 1rem;
}

nav ul {
    display: flex;
    list-style: none;
}

nav li {
    margin-right: 20px;
}

nav a {
    color: white;
    text-decoration: none;
    padding: 5px 0;
}

nav a:hover {
    border-bottom: 2px solid white;
}

/* Main content */
main {
    padding: 20px;
}

/* Card styles */
.card {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.card-header {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 1.2rem;
    font-weight: bold;
}

/* Button styles */
.btn {
    display: inline-block;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    font-weight: bold;
    border: none;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-success {
    background-color: #2ecc71;
    color: white;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

/* Form styles */
.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

input, select, textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Table styles */
table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

tr:hover {
    background-color: #f5f5f5;
}

/* Alert styles */
.alert {
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Grid layout */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.col {
    flex: 1;
    padding: 0 10px;
}

/* Dashboard specific styles */
.dashboard-header {
    margin-bottom: 20px;
}

.dashboard-header h1 {
    font-size: 2rem;
    color: #2c3e50;
}

.project-header, .user-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.project-header h1, .user-header h1 {
    margin-left: 15px;
    font-size: 1.8rem;
    color: #2c3e50;
}

/* Project and User Item Mapping Styles */
.item-checkboxes {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.item-checkbox-row {
    padding: 8px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
}

.item-checkbox-row:last-child {
    border-bottom: none;
}

.item-checkbox-row input[type="checkbox"] {
    width: auto;
    margin-right: 10px;
}

.item-checkbox-row label {
    margin-bottom: 0;
    flex-grow: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.badge {
    display: inline-block;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: bold;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 10px;
}

.badge-info {
    background-color: #3498db;
    color: white;
}

.form-actions {
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
}

/* Project Items Section */
.project-items-section {
    margin-bottom: 25px;
    border: 1px solid #eee;
    border-radius: 5px;
    padding: 15px;
    background-color: #f9f9f9;
}

.project-items-section h3 {
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ddd;
    color: #2c3e50;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .row {
        flex-direction: column;
    }

    .col {
        margin-bottom: 15px;
    }

    nav ul {
        flex-direction: column;
    }

    nav li {
        margin-bottom: 10px;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions button {
        margin-bottom: 10px;
    }
}

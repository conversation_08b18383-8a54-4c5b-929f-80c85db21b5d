{% extends "base.html" %}

{% block title %}Project Details - IPM Admin Panel{% endblock %}

{% block content %}
<div class="project-header">
    <a href="/web/dashboard" class="btn btn-primary">← Back to Dashboard</a>
    <h1>Project Details: {{ project.name }}</h1>
</div>

<div class="card">
    <div class="card-header">
        <h2 class="card-title">Project Information</h2>
    </div>

    <div class="project-info">
        <div class="row">
            <div class="col">
                <p><strong>Name:</strong> {{ project.name }}</p>
                <p><strong>Description:</strong> {{ project.description or 'No description' }}</p>
                <p><strong>Created:</strong> {{ project.created_at if project.created_at else 'N/A' }}</p>
            </div>
            <div class="col">
                <p><strong>PO Balance:</strong> ₹{{ project.po_balance }}</p>
                <p><strong>Estimated Balance:</strong> ₹{{ project.estimated_balance }}</p>
                <p><strong>Actual Balance:</strong> ₹{{ project.actual_balance }}</p>
            </div>
        </div>

        {% if project.file_path %}
        <div class="project-file">
            <p><strong>Project File:</strong> <a href="/uploads/{{ project.file_path }}" target="_blank">View File</a></p>
        </div>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Project Users</h2>
                <button class="btn btn-primary" onclick="toggleForm('user-mapping-form')">Add User</button>
            </div>

            <!-- User Mapping Form -->
            <div id="user-mapping-form" style="display: none;">
                <form action="/web/project_mapping" method="post">
                    <input type="hidden" name="project_id" value="{{ project.uuid }}">

                    <div class="form-group">
                        <label for="user_id">Select User</label>
                        <select id="user_id" name="user_id" required>
                            <option value="">-- Select User --</option>
                            {% for user in all_users %}
                            <option value="{{ user.uuid }}">{{ user.name }} ({{ user.role }})</option>
                            {% endfor %}
                        </select>
                    </div>

                    <button type="submit" class="btn btn-success">Assign User</button>
                    <button type="button" class="btn btn-danger" onclick="toggleForm('user-mapping-form')">Cancel</button>
                </form>
            </div>

            <!-- Project Users List -->
            <div class="project-users-list">
                <table>
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Phone</th>
                            <th>Role</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user_data in project_users %}
                        <tr>
                            <td>{{ user_data.name }}</td>
                            <td>{{ user_data.phone }}</td>
                            <td>{{ user_data.role }}</td>
                            <td>
                                <a href="/web/users/{{ user_data.uuid }}" class="btn btn-primary">View</a>
                                <button class="btn btn-success" onclick="toggleForm('user-item-mapping-form-{{ user_data.uuid }}')">Assign Items</button>
                                <form action="/web/project_mapping/remove" method="post" style="display: inline;">
                                    <input type="hidden" name="project_id" value="{{ project.uuid }}">
                                    <input type="hidden" name="user_id" value="{{ user_data.uuid }}">
                                    <button type="submit" class="btn btn-danger" onclick="return confirmDelete('Remove this user from the project?')">Remove</button>
                                </form>

                                <!-- User Item Mapping Form -->
                                <div id="user-item-mapping-form-{{ user_data.uuid }}" style="display: none; margin-top: 10px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                    <h4 class="mb-3">Select Project Items to Assign to {{ user_data.name }}</h4>
                                    <form action="/web/user_item_mapping" method="post">
                                        <input type="hidden" name="project_id" value="{{ project.uuid }}">
                                        <input type="hidden" name="user_id" value="{{ user_data.uuid }}">

                                        <div class="form-group">
                                            <div class="item-checkboxes" style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px;">
                                                {% for item_data in project_items %}
                                                <div class="item-checkbox-row" style="padding: 8px; border-bottom: 1px solid #eee; display: flex; align-items: center;">
                                                    <input type="checkbox" id="item-{{ item_data.uuid }}-{{ user_data.uuid }}" name="item_ids" value="{{ item_data.uuid }}" style="margin-right: 10px;">
                                                    <label for="item-{{ item_data.uuid }}-{{ user_data.uuid }}" style="margin-bottom: 0; flex-grow: 1;">
                                                        <strong>{{ item_data.name }}</strong>
                                                        <span class="badge badge-info" style="margin-left: 10px;">₹{{ item_data.remaining_balance }}</span>
                                                    </label>
                                                </div>
                                                {% endfor %}
                                            </div>
                                        </div>

                                        <div class="form-actions" style="margin-top: 15px; display: flex; justify-content: space-between;">
                                            <button type="button" class="btn btn-danger" onclick="toggleForm('user-item-mapping-form-{{ user_data.uuid }}')">Cancel</button>
                                            <button type="submit" class="btn btn-success">Assign Selected Items</button>
                                        </div>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="col">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Project Items</h2>
                <button class="btn btn-primary" onclick="toggleForm('item-mapping-form')">Add Item</button>
            </div>

            <!-- Item Mapping Form -->
            <div id="item-mapping-form" style="display: none;">
                <form action="/web/item_mapping" method="post">
                    <input type="hidden" name="project_id" value="{{ project.uuid }}">

                    <div class="form-group">
                        <label for="item_id">Select Item</label>
                        <select id="item_id" name="item_id" required>
                            <option value="">-- Select Item --</option>
                            {% for item in all_items %}
                            <option value="{{ item.uuid }}">{{ item.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="item_balance">Item Balance</label>
                        <input type="number" id="item_balance" name="item_balance" step="0.01" required>
                    </div>

                    <button type="submit" class="btn btn-success">Assign Item</button>
                    <button type="button" class="btn btn-danger" onclick="toggleForm('item-mapping-form')">Cancel</button>
                </form>
            </div>

            <!-- Project Items List -->
            <div class="project-items-list">
                <table>
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Balance</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item_data in project_items %}
                        <tr>
                            <td>{{ item_data.name }}</td>
                            <td>₹{{ item_data.remaining_balance }}</td>
                            <td>
                                <form action="/web/item_mapping/remove" method="post" style="display: inline;">
                                    <input type="hidden" name="project_id" value="{{ project.uuid }}">
                                    <input type="hidden" name="item_id" value="{{ item_data.uuid }}">
                                    <button type="submit" class="btn btn-danger" onclick="return confirmDelete('Remove this item from the project?')">Remove</button>
                                </form>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Invoices</h2>
                <button class="btn btn-primary" onclick="toggleForm('invoice-form')">Add Invoice</button>
            </div>

            <!-- Invoice Form -->
            <div id="invoice-form" style="display: none;">
                <form action="/web/invoices" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="project_id" value="{{ project.uuid }}">

                    <div class="form-group">
                        <label for="amount">Amount</label>
                        <input type="number" id="amount" name="amount" step="0.01" required>
                    </div>

                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="invoice_file">Invoice File (PDF)</label>
                        <input type="file" id="invoice_file" name="invoice_file" accept=".pdf">
                    </div>

                    <button type="submit" class="btn btn-success">Create Invoice</button>
                    <button type="button" class="btn btn-danger" onclick="toggleForm('invoice-form')">Cancel</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function toggleForm(formId) {
        const form = document.getElementById(formId);
        if (form.style.display === 'none') {
            form.style.display = 'block';
        } else {
            form.style.display = 'none';
        }
    }

    function confirmDelete(message) {
        return confirm(message || 'Are you sure you want to delete this item?');
    }
</script>
{% endblock %}

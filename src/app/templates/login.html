{% extends "base.html" %}

{% block title %}Login - IPM Admin Panel{% endblock %}

{% block extra_css %}
<style>
    body {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background-color: #f5f5f5;
    }
    
    .login-container {
        width: 100%;
        max-width: 400px;
        padding: 30px;
        background-color: white;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .login-header {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .login-header h1 {
        font-size: 24px;
        color: #2c3e50;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #333;
    }
    
    .form-group input {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 16px;
    }
    
    .login-btn {
        width: 100%;
        padding: 12px;
        background-color: #3498db;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 16px;
        cursor: pointer;
        transition: background-color 0.3s;
    }
    
    .login-btn:hover {
        background-color: #2980b9;
    }
    
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 20px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-header">
        <h1>IPM Admin Panel</h1>
    </div>
    
    {% if error %}
    <div class="error-message">
        {{ error }}
    </div>
    {% endif %}
    
    <form method="post" action="/web/login">
        <div class="form-group">
            <label for="phone">Phone Number</label>
            <input type="number" id="phone" name="phone" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password</label>
            <input type="password" id="password" name="password" required>
        </div>
        
        <button type="submit" class="login-btn">Login</button>
    </form>
</div>
{% endblock %}

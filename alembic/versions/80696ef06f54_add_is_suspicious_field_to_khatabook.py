"""Add is_suspicious field to Khatabook

Revision ID: 80696ef06f54
Revises: 7fa87240af97
Create Date: 2025-05-05 21:58:35.211058

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '80696ef06f54'
down_revision: Union[str, None] = '7fa87240af97'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('khatabook_entries', sa.Column('is_suspicious', sa.<PERSON>(), server_default='false', nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('khatabook_entries', 'is_suspicious')
    # ### end Alembic commands ###

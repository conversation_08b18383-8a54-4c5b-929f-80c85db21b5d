# IPM Application Environment Variables
# Copy this file to .env and update the values as needed

# Database Configuration
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password
DB_HOST=your_db_host
DB_PORT=5432
DB_NAME=your_db_name

# File Upload Configuration
UPLOADS_DIR=/app/uploads
SERVICE_FILE=/app/src/app/utils/firebase/secret_files.json

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# Twilio SMS Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_VERIFY_SERVICE_SID=your_twilio_verify_service_sid

# Application Configuration
HOST_URL=https://your-domain.com

# Logging Configuration
LOG_LEVEL=INFO
LOG_DIR=/app/logs

# Note: LOG_LEVEL can be one of: DEBUG, <PERSON>FO, WARNING, ERROR, CRITICAL
# Note: LOG_DIR should be an absolute path where log files will be stored
# In Docker, this should typically be /app/logs which is mounted to ./logs on the host

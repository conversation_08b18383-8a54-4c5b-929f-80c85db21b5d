<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        #response {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>CORS Test for IPM API</h1>
    <p>This page tests CORS connectivity to the IPM API server.</p>
    
    <button id="testLogin">Test Login API</button>
    <div id="response"></div>

    <h2>Instructions for Frontend Developers</h2>
    <p>If you're experiencing CORS issues with the IPM API, try the following approaches:</p>
    
    <h3>1. Use the fetch API with credentials</h3>
    <pre>
fetch('http://***********:8000/auth/login', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    },
    body: JSON.stringify({
        phone: "1234567890",
        password: "supersecurepassword",
        device_id: ""
    })
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));
    </pre>

    <h3>2. Use axios with proper configuration</h3>
    <pre>
import axios from 'axios';

axios.post('http://***********:8000/auth/login', {
    phone: "1234567890",
    password: "supersecurepassword",
    device_id: ""
}, {
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
})
.then(response => console.log(response.data))
.catch(error => console.error('Error:', error));
    </pre>

    <h3>3. If you're still having issues, try using a CORS proxy</h3>
    <pre>
// Using a CORS proxy service
fetch('https://cors-anywhere.herokuapp.com/http://***********:8000/auth/login', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    },
    body: JSON.stringify({
        phone: "1234567890",
        password: "supersecurepassword",
        device_id: ""
    })
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));
    </pre>

    <script>
        document.getElementById('testLogin').addEventListener('click', function() {
            const responseDiv = document.getElementById('response');
            responseDiv.innerHTML = '<p>Testing connection to API...</p>';
            
            fetch('http://***********:8000/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    phone: "1234567890",
                    password: "supersecurepassword",
                    device_id: ""
                })
            })
            .then(response => response.json())
            .then(data => {
                responseDiv.innerHTML = '<p>Success! API responded with:</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                responseDiv.innerHTML = '<p>Error connecting to API:</p><pre>' + error + '</pre>';
                console.error('Error:', error);
            });
        });
    </script>
</body>
</html>

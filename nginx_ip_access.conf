# Additional Nginx configuration to handle IP-based access
# Add this to your nginx configuration

server {
    listen 80;
    server_name *************;
    
    # Redirect HTTP to HTTPS for IP access
    return 301 https://dashboard.inqilabgroup.com$request_uri;
}

# Alternative: Allow direct HTTP access via IP (less secure)
server {
    listen 80;
    server_name *************;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Connection and timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        proxy_buffering off;
        proxy_request_buffering off;
        
        # Keep-alive settings
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        
        # Handle client disconnections gracefully
        proxy_ignore_client_abort off;
        
        # Buffer settings for better performance
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }

    location /uploads/ {
        alias /root/IPM/uploads/;
        autoindex off;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        add_header Content-Disposition "inline";
        try_files $uri $uri/ =404;
    }

    location /static/ {
        alias /root/IPM/src/app/static/;
        autoindex off;
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
        try_files $uri $uri/ =404;
    }
}
